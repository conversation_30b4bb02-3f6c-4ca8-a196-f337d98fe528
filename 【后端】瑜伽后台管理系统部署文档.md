# 瑜伽后台管理系统部署文档

## 1. 系统概述

瑜伽后台管理系统是一个基于前后端分离架构的SaaS平台，用于管理瑜伽工作室的各项业务，包括会员管理、课程管理、预约系统、教练管理、收银系统等功能。系统分为前端和后端两个部分，需要分别部署。

## 2. 技术栈概述

### 2.1 后端技术栈

- **编程语言**: Python 3.10+
- **Web框架**: FastAPI 0.109.2
- **ORM**: SQLAlchemy 2.0.25
- **数据库**: MySQL 8.0
- **认证**: JWT (python-jose 3.3.0)
- **密码加密**: Passlib (bcrypt) 1.7.4
- **API文档**: Swagger UI (FastAPI内置)
- **数据验证**: Pydantic 2.6.1
- **数据库迁移**: Alembic 1.13.1
- **WSGI服务器**: Uvicorn 0.27.1

### 2.2 前端技术栈

- **框架**: Next.js 15.2.4
- **UI库**: React 19
- **组件库**: Radix UI, Shadcn UI
- **样式**: Tailwind CSS
- **状态管理**: React Context API
- **HTTP客户端**: Axios
- **表单处理**: React Hook Form
- **数据验证**: Zod
- **图表**: Recharts

## 3. 系统要求

### 3.1 服务器要求

#### 最低配置
- **CPU**: 2核
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **操作系统**: Ubuntu 20.04 LTS 或 CentOS 8+

#### 推荐配置
- **CPU**: 4核
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **操作系统**: Ubuntu 22.04 LTS 或 CentOS 8+

### 3.2 软件要求

- **Python**: 3.10+
- **Node.js**: 18.0.0+
- **npm/pnpm**: 最新版本
- **MySQL**: 8.0+
- **Nginx**: 1.18.0+
- **Git**: 2.25.0+

## 4. 后端部署

### 4.1 准备工作

1. 安装必要的系统依赖:

```bash
# Ubuntu
sudo apt update
sudo apt install -y python3 python3-pip python3-venv git nginx mysql-server

# CentOS
sudo dnf install -y python3 python3-pip git nginx mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

2. 创建数据库和用户:

```bash
sudo mysql -u root -p
```

在MySQL命令行中执行:

```sql
CREATE DATABASE yoga_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'yoga_user'@'localhost' IDENTIFIED BY 'yoga_password';
GRANT ALL PRIVILEGES ON yoga_db.* TO 'yoga_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4.2 部署后端应用

1. 克隆代码仓库:

```bash
git clone <后端仓库URL> /var/www/yoga-backend
cd /var/www/yoga-backend
```

2. 创建并激活虚拟环境:

```bash
python3 -m venv venv
source venv/bin/activate
```

3. 安装依赖:

```bash
pip install -r requirements.txt
```

4. 创建环境变量文件:

```bash
cp .env.example .env
```

5. 编辑环境变量文件，设置数据库连接和其他配置:

```bash
nano .env
```

填入以下内容（根据实际情况修改）:

```
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=yoga_user
DB_PASSWORD=yoga_password
DB_NAME=yoga_db

# JWT配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 服务器配置
HOST=0.0.0.0
PORT=8000
```

6. 初始化数据库:

```bash
python init_db.py
```

7. 创建Systemd服务文件:

```bash
sudo nano /etc/systemd/system/yoga-backend.service
```

填入以下内容:

```
[Unit]
Description=Yoga Backend Service
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/yoga-backend
ExecStart=/var/www/yoga-backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=5
SyslogIdentifier=yoga-backend

[Install]
WantedBy=multi-user.target
```

8. 启动服务:

```bash
sudo systemctl daemon-reload
sudo systemctl start yoga-backend
sudo systemctl enable yoga-backend
```

9. 检查服务状态:

```bash
sudo systemctl status yoga-backend
```

### 4.3 配置Nginx反向代理

1. 创建Nginx配置文件:

```bash
sudo nano /etc/nginx/sites-available/yoga-backend
```

填入以下内容:

```nginx
server {
    listen 80;
    server_name api.yourdomain.com;  # 替换为你的域名

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

2. 启用站点配置:

```bash
sudo ln -s /etc/nginx/sites-available/yoga-backend /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 5. 前端部署

### 5.1 准备工作

1. 安装Node.js和npm:

```bash
# Ubuntu
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# CentOS
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo dnf install -y nodejs
```

2. 安装pnpm (可选，如果使用pnpm):

```bash
npm install -g pnpm
```

### 5.2 部署前端应用

1. 克隆代码仓库:

```bash
git clone <前端仓库URL> /var/www/yoga-frontend
cd /var/www/yoga-frontend
```

2. 安装依赖:

```bash
# 使用npm
npm install

# 或使用pnpm
pnpm install
```

3. 创建环境变量文件:

```bash
cp .env.local.example .env.local
```

4. 编辑环境变量文件:

```bash
nano .env.local
```

填入以下内容（根据实际情况修改）:

```
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com
```

5. 构建生产版本:

```bash
# 使用npm
npm run build

# 或使用pnpm
pnpm build
```

6. 创建Systemd服务文件:

```bash
sudo nano /etc/systemd/system/yoga-frontend.service
```

填入以下内容:

```
[Unit]
Description=Yoga Frontend Service
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/yoga-frontend
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=5
SyslogIdentifier=yoga-frontend

[Install]
WantedBy=multi-user.target
```

7. 启动服务:

```bash
sudo systemctl daemon-reload
sudo systemctl start yoga-frontend
sudo systemctl enable yoga-frontend
```

8. 检查服务状态:

```bash
sudo systemctl status yoga-frontend
```

### 5.3 配置Nginx反向代理

1. 创建Nginx配置文件:

```bash
sudo nano /etc/nginx/sites-available/yoga-frontend
```

填入以下内容:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;  # 替换为你的域名

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

2. 启用站点配置:

```bash
sudo ln -s /etc/nginx/sites-available/yoga-frontend /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 6. SSL配置（推荐）

使用Let's Encrypt配置SSL:

```bash
sudo apt install -y certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
sudo certbot --nginx -d api.yourdomain.com
```

按照提示完成SSL证书的配置。

## 7. 防火墙配置

配置防火墙只允许必要的端口:

```bash
# Ubuntu (ufw)
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 8. 数据库备份

设置定时备份MySQL数据库:

1. 创建备份脚本:

```bash
sudo nano /usr/local/bin/backup-yoga-db.sh
```

填入以下内容:

```bash
#!/bin/bash
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/var/backups/yoga-db"
MYSQL_USER="yoga_user"
MYSQL_PASSWORD="yoga_password"
DATABASE="yoga_db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u$MYSQL_USER -p$MYSQL_PASSWORD $DATABASE | gzip > $BACKUP_DIR/yoga_db_$TIMESTAMP.sql.gz

# 删除7天前的备份
find $BACKUP_DIR -name "yoga_db_*.sql.gz" -type f -mtime +7 -delete
```

2. 设置执行权限:

```bash
sudo chmod +x /usr/local/bin/backup-yoga-db.sh
```

3. 添加到crontab:

```bash
sudo crontab -e
```

添加以下行:

```
0 2 * * * /usr/local/bin/backup-yoga-db.sh
```

## 9. 日志管理

配置日志轮转:

```bash
sudo nano /etc/logrotate.d/yoga
```

填入以下内容:

```
/var/www/yoga-backend/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data www-data
}

/var/www/yoga-frontend/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data www-data
}
```

## 10. 监控设置

### 10.1 安装Prometheus和Grafana (可选)

```bash
# 安装Prometheus
sudo apt install -y prometheus

# 安装Grafana
sudo apt-get install -y apt-transport-https software-properties-common
wget -q -O - https://packages.grafana.com/gpg.key | sudo apt-key add -
echo "deb https://packages.grafana.com/oss/deb stable main" | sudo tee -a /etc/apt/sources.list.d/grafana.list
sudo apt update
sudo apt install -y grafana
sudo systemctl enable grafana-server
sudo systemctl start grafana-server
```

## 11. 故障排查

### 11.1 检查服务状态

```bash
# 检查后端服务
sudo systemctl status yoga-backend

# 检查前端服务
sudo systemctl status yoga-frontend

# 检查Nginx服务
sudo systemctl status nginx

# 检查MySQL服务
sudo systemctl status mysql
```

### 11.2 查看日志

```bash
# 查看后端日志
sudo journalctl -u yoga-backend

# 查看前端日志
sudo journalctl -u yoga-frontend

# 查看Nginx日志
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

## 12. 系统更新

### 12.1 更新后端

```bash
cd /var/www/yoga-backend
git pull
source venv/bin/activate
pip install -r requirements.txt
sudo systemctl restart yoga-backend
```

### 12.2 更新前端

```bash
cd /var/www/yoga-frontend
git pull
npm install  # 或 pnpm install
npm run build  # 或 pnpm build
sudo systemctl restart yoga-frontend
```

## 13. 联系与支持

如有部署问题，请联系系统管理员或开发团队。
