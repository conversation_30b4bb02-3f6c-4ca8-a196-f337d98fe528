# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Local configuration
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.sqlite3
*.db

# OS specific
.DS_Store
Thumbs.db

# Alembic
migrations/versions/

# Jupyter Notebook
.ipynb_checkpoints

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
